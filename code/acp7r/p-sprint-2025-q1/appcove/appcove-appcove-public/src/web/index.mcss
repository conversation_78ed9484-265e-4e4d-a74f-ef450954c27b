            about-appcove {
                /* Paper texture background */
                background-image:
                    radial-gradient(circle at 20% 50%, rgba(120, 119, 108, 0.03) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(120, 119, 108, 0.03) 0%, transparent 50%),
                    radial-gradient(circle at 40% 80%, rgba(120, 119, 108, 0.03) 0%, transparent 50%),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.95) 0%, rgba(250, 248, 245, 0.95) 100%);
                background-size: 300px 300px, 200px 200px, 250px 250px, 100% 100%;
                background-position: 0 0, 100px 100px, 200px 50px, 0 0;
                min-height: 100vh;
                position: relative;

                /* Add subtle paper texture overlay */
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-image:
                        repeating-linear-gradient(
                            0deg,
                            transparent,
                            transparent 2px,
                            rgba(120, 119, 108, 0.01) 2px,
                            rgba(120, 119, 108, 0.01) 4px
                        ),
                        repeating-linear-gradient(
                            90deg,
                            transparent,
                            transparent 2px,
                            rgba(120, 119, 108, 0.01) 2px,
                            rgba(120, 119, 108, 0.01) 4px
                        );
                    pointer-events: none;
                    z-index: 1;
                }

                /* Ensure content appears above the texture overlay */
                > * {
                    position: relative;
                    z-index: 2;
                }

                #page-heading {
                    margin-top: 1rem;
                    margin-left: -1.5rem;

                    h1 {
                        font-size: 50px;
                        color: #2C4861;
                        margin-bottom: 1rem;
                        line-height: 1.6;
                        font-style: Ralewayregular;
                    }

                    p {
                        font-size: 2rem;
                        color: #00bfa5;
                        margin-bottom: 1rem;
                        line-height: 1.6;
                        font-weight: 600;
                        font-style: Open Sans;
                    }
                    .circle-bg {
                        position: absolute;
                        width: 100px;
                        height: 100px;
                        background-color: #fff9c4;
                        border-radius: 50%;
                        top: -150px;
                        left: -200px;
                    }
                    #float-image {
                        max-width: 350px;
                        height: auto;
                        margin-right: 10px;
                        margin-bottom: 0px;
                    }

                    strong {
                        color: #012650f8;
                        font-weight: bold;
                    }
                    .float-image {
                        float: left;
                        margin-right: 10px;
                        margin-bottom: 0px;
                    }  
                }
                #page-body {
                    margin-top: 0.5rem;
                    margin-left: -1.5rem;
                    header {
                        p {
                            font-size: 1.5rem;
                            color: #21536C;
                            margin-bottom: 1rem;
                            line-height: 1.6;
                            font-weight: bold;
                            font-style: Open Sans;
                        }
                    }
                }

                #page-body {
                    margin-top: 0.5rem;
                    margin-left: -1.5rem;

                        p {
                            font-size: 1rem;
                            color: #000000;
                            margin-bottom: 1rem;
                            line-height: 1.6;
                            font-weight: bold;
                            font-style: Open Sans;
                        }
                        strong {
                            color: #012650f8;
                            font-weight: bold;
                        }
                        br {
                            margin-bottom: 0.8rem;
                        }
                        content {
                        font-size: 1.2rem;
                        color: #4A4A4A;
                        line-height: 1.6;
                        font-style: Open Sans;
                        }
                    }

                #page-footer {
                    margin-top: 1.5rem;
                    margin-left: -1.5rem;

                    p {
                        margin-bottom: 0.8rem;
                        font-size: 1.2rem;
                        color: #000000;
                        line-height: 1.6;
                        font-weight: bold;
                        font-style: Open Sans regular;
                    }
                    strong {
                        color: #21536C;
                        font-weight: bold;
                        font-style: Open Sans bold;
                    }
                    
                    br {
                        margin-bottom: 0.8rem;
                    }    
                }

                #page-main {
                    margin-top: 1.5rem;
                    margin-left: -1.5rem;
                    display: flex;
                    flex-direction: column;
                    gap: 2rem;

                    header-2 {
                        font-size: 2rem;
                        color: #012650f8;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    p {
                        font-size: 1rem;
                        color: #4A4A4A;
                        line-height: 1.6;
                    }
                    br {
                        margin-bottom: 0.5rem;
                    }
                    header-3 {
                        font-size: 2rem;
                        color: #012650f8;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    hr {
                        all: initial;
                        display: block;
                        border-bottom: 3.2px dotted rgb(9, 81, 129);
                    }   
                    .arrow-icon-wrapper {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 3rem;
                        height: 3rem;
                        border-radius: 80%;
                        background-color: #007b8a;
                        margin-top: 2rem;
                        margin-bottom: 3rem;
                        cursor: pointer;
                        transition: all 0.3s ease;

                    &:hover {
                        background-color: #0887a7;
                    }
                    .arrow-icon {
                        color: white;
                        font-size: 2rem;
                        transition: all 0.3s ease;
                    }
                }
            }

                #page-body {
                    margin-bottom: 1rem;
                    margin-top: 0.5rem;
                    margin-left: -1.5rem;

                    h6 {
                        font-size: 2rem;
                        color: #009688;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                }
                .staff-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 350px);
                    gap: 20px 25px; /* row-gap column-gap */

                }
                .staff-card {
                    background-color: #F5F0E9;
                    border-radius: 12px;
                    width: 350px;
                    padding: 20px;
                    margin-bottom: 0;
                    height: 120px;
                    flex-direction: inherit;
                    justify-content: center;
                h4 {
                        font-size: 1.3rem;
                        color: #000000;
                        font-weight: bold;
                        font-style: open sans;
                }
                p   {
                        font-size: 1rem;
                        color: #333333;
                        
                    }
                years {
                        font-size: 1rem;
                        color:  #009688;
                        font-weight: bold;
                        font-style: open sans;
                    }
                }

                #page-footer {
                    margin-top: 1.5rem;
                    margin-bottom: 2rem;
                    margin-left: -1.5rem;

                    p {
                        font-size: 1.2rem;
                        color: #1f2527;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    strong {
                        font-weight: bold;
                    }
                }

                #page-body {
                    margin-top: 1.5rem;
                    margin-bottom: 2rem;
                    margin-left: -1.5rem;

                    p {
                        font-size: 1.2rem;
                        color: #1f2527;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    strong  {
                        font-weight: bold;
                    }
                    br {
                        margin-bottom: 0.8rem;
                        margin-top: 0.8rem;
                    }
                }

                #timeline-panel {
                        background-color: #e8ebe0a8;
                        border-radius: 0.5rem;
                        padding: 1rem 1.25rem;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        width: 550px;
                        margin-left: -1.5rem;
                        margin-top: -1rem;
                    
                    h5 {
                        font-weight: bold;
                        color: #21536C;
                        font-size: 2rem;
                        line-height: 1;
                    }
                    p.notable{
                        font-size: 1rem;
                        color: #05475e;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    .timeline-container {
                        position: relative;
                        margin-left: 10px;
                        border-left: 2px dotted #129686;
                        padding: 20px 0;
                        margin-top: 1rem;
                        margin-bottom: 1rem;
                        max-width: 1000px;
                    }
                    .timeline-item {
                        position: relative;
                        padding-left: 1rem;
                        margin-bottom: 2rem;
                    }
                    .timeline-dot {
                        height: 16px;
                        width: 16px;
                        background-color: #119191;
                        border-radius: 50%;
                        display: inline-block;
                        margin-right: 8px;
                        position: absolute;
                        left: -7px;
                        top: 3px;
                    }
                    .timeline-year {
                        font-weight: bold;
                        color: #054f5a;
                    }
                    .active-project {
                        font-weight: 600;
                        color: #119191;
                        font-style: italic;
                    }
                    .inactive-project {
                        font-weight: 600;
                        color: #292c2e;
                        font-style: italic;
                    }
                    strong {
                        color: #01070ff8;
                        font-weight: bold;
                        font-size: 1.3rem;
                    }
                    small {
                        color:#4A4A4A ;
                        font-weight: bold;
                        font-size: 1.2rem;
                    }
                }
                    br {
                        margin-bottom: 0.8rem;
                        margin-top: 0.8rem;
                    }
                    .company-logo {
                        justify-content: left;
                        align-items: left;
                        margin-top: 2rem;
                        
                    img {
                        height: 50px;
                        width: auto;
                    }
                }
            }
        
        
            
        
    
              
